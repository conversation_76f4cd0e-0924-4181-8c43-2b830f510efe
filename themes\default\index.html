<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>{#$site_title#}</title>
    <meta name="keywords" content="{#$site_keywords#}" />
    <meta name="description" content="{#$site_description#}" />
    <meta name="author" content="95目录网" />
    <meta name="copyright" content="Powered By 95dir.com" />
    <meta name="robots" content="index,follow" />
    <meta name="googlebot" content="index,follow" />
    <meta name="baiduspider" content="index,follow" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <!-- SEO优化 - Open Graph标签 -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="{#$site_title#}" />
    <meta property="og:description" content="{#$site_description#}" />
    <meta property="og:url" content="{#$site_url#}" />
    <meta property="og:site_name" content="95目录网" />
    <meta property="og:locale" content="zh_CN" />

    <!-- SEO优化 - Twitter Card标签 -->
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="{#$site_title#}" />
    <meta name="twitter:description" content="{#$site_description#}" />

    <!-- SEO优化 - 结构化数据 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "95目录网",
        "url": "{#$site_url#}",
        "description": "{#$site_description#}",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{#$site_url#}?mod=search&query={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>

    <!-- SEO优化 - 网站验证 -->
    <meta name="baidu-site-verification" content="codeva-{#$site_url|md5#}" />
    <meta name="google-site-verification" content="google-{#$site_url|md5#}" />

    <!-- SEO优化 - 规范链接 -->
    <link rel="canonical" href="{#$site_url#}" />

    <!-- SEO优化 - 网站图标 -->
    <link rel="icon" type="image/x-icon" href="{#$site_root#}favicon.ico" />
    <link rel="shortcut icon" type="image/x-icon" href="{#$site_root#}favicon.ico" />
    <link rel="apple-touch-icon" href="{#$site_root#}favicon.ico" />

    <!-- SEO优化 - DNS预解析 -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com" />
    <link rel="dns-prefetch" href="//t0.gstatic.cn" />
    <link rel="dns-prefetch" href="//www.google.com" />
    <link rel="dns-prefetch" href="//www.baidu.com" />

    <!-- SEO优化 - 预连接重要资源 -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin />
    <link rel="preconnect" href="https://t0.gstatic.cn" crossorigin />

    <!-- ========== 样式表 ========= -->
    <link href="{#$site_root#}themes/default/skin/nav.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
          integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
          crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- ========== 脚本 ========= -->
    <script type="text/javascript">
        var sitepath = '{#$site_root#}';
        var rewrite  = '{#$cfg.link_struct#}';
    </script>
    <script type="text/javascript" src="{#$site_root#}public/scripts/jquery.min.js"></script>
    <script type="text/javascript" src="{#$site_root#}public/scripts/common.js"></script>

    <!-- 站长推荐美化样式 -->
    <style>
        /* 推荐列表样式 */
        .bestlist-enhanced {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            gap: 2px;
            margin: 0;
            padding: 8px;
            list-style: none;
        }

        .recommend-item {
            position: relative;
        }

        .recommend-link {
            display: block;
            background: #fff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 8px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-height: 70px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .recommend-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
            transition: left 0.5s;
        }

        .recommend-link:hover::before {
            left: 100%;
        }

        .recommend-link:hover {
            border-color: #ffd700;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
            background: linear-gradient(135deg, #fff 0%, #fffbf0 100%);
        }

        .recommend-icon {
            position: relative;
            margin-bottom: 8px;
        }

        .recommend-icon img {
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .recommend-badge {
            position: absolute;
            top: -8px;
            right: -12px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            font-size: 8px;
            padding: 1px 4px;
            border-radius: 6px;
            font-weight: bold;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            animation: bounce 2s infinite;
            z-index: 10;
        }

        .recommend-name {
            font-size: 11px;
            text-align: center;
            line-height: 1.2;
            font-weight: 500;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            max-width: 100%;
        }

        /* 动画效果 */
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-3px); }
            60% { transform: translateY(-2px); }
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .bestlist-enhanced {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                gap: 6px;
            }

            .recommend-link {
                padding: 8px 6px;
                min-height: 50px;
            }

            .recommend-name {
                font-size: 10px;
            }

            .recommend-icon img {
                width: 24px;
                height: 24px;
            }
        }

        @media (max-width: 480px) {
            .bestlist-enhanced {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 5px;
            }
        }

        /* 省份标签样式 - 继承hcatelist样式并添加特殊效果 */
        .province-tags-list .province-tag {
            display: inline-block;
            padding: 5px 10px;
            background: #eaeef3;
            border-radius: 3px;
            color: #555;
            font-size: 13px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .province-tags-list .province-tag:hover {
            background: #1791de;
            color: #fff;
        }

        /* 不同类型省份的特殊样式 */
        .province-tags-list .province-tag.municipality {
            background: #ffe6e6;
            color: #dc3545;
        }

        .province-tags-list .province-tag.municipality:hover {
            background: #dc3545;
            color: #fff;
        }

        .province-tags-list .province-tag.autonomous {
            background: #e6f7e6;
            color: #28a745;
        }

        .province-tags-list .province-tag.autonomous:hover {
            background: #28a745;
            color: #fff;
        }

        .province-tags-list .province-tag.special {
            background: #fff3cd;
            color: #856404;
        }

        .province-tags-list .province-tag.special:hover {
            background: #ffc107;
            color: #212529;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .province-tags-list .province-tag {
                font-size: 12px;
                padding: 4px 8px;
            }
        }

        @media (max-width: 480px) {
            .province-tags-list .province-tag {
                font-size: 11px;
                padding: 3px 6px;
            }
        }

        /* 点击动画效果 */
        .province-tag.clicked {
            transform: scale(0.95);
            transition: transform 0.1s ease;
        }

        /* 统计容器样式 - 与友情链接容器保持一致 */
        #todayStatsBox, #spiderStatsBox {
            border: 1px solid #dae7ed;
            padding: 8px;
        }

        #todayStatsBox h3, #spiderStatsBox h3 {
            height: 20px;
            float: left;
            width: auto;
            margin-bottom: 10px;
            font-size: 14px;
            color: #333;
        }

        /* 统计卡片网格布局 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
            margin-top: 30px;
            clear: both;
        }

        .spider-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 30px;
            clear: both;
        }

        /* 访问统计卡片样式 */
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            padding: 12px 8px;
            text-align: center;
            color: white;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .stat-card:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .stat-icon {
            font-size: 20px;
            margin-bottom: 6px;
            animation: pulse 2s infinite;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.9;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .stat-value {
            font-size: 16px;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* 蜘蛛统计卡片样式 */
        .spider-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 8px;
            padding: 10px 6px;
            text-align: center;
            color: white;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            min-height: 70px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .spider-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.4s;
        }

        .spider-card:hover::before {
            left: 100%;
        }

        .spider-card:hover {
            transform: translateY(-3px) scale(1.08);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .spider-icon {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 4px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .spider-label {
            font-size: 10px;
            opacity: 0.9;
            margin-bottom: 3px;
            font-weight: 500;
        }

        .spider-value {
            font-size: 14px;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* 工具提示样式 */
        .stat-card[data-tooltip]:hover::after,
        .spider-card[data-tooltip]:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        /* 脉冲动画 */
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* 淡入动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(-50%) translateY(5px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
        }

        /* 数字更新动画 */
        .stats-number {
            transition: all 0.3s ease;
        }

        .stats-number.updated {
            animation: numberUpdate 0.6s ease;
        }

        @keyframes numberUpdate {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); color: #ffd700; }
            100% { transform: scale(1); }
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            #todayStatsBox, #spiderStatsBox {
                margin: 1%;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
                margin-top: 25px;
            }

            .spider-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 6px;
                margin-top: 25px;
            }

            .stat-card {
                padding: 10px 6px;
                min-height: 70px;
            }

            .spider-card {
                padding: 8px 4px;
                min-height: 60px;
            }

            .stat-icon {
                font-size: 18px;
            }

            .spider-icon {
                font-size: 16px;
            }

            .stat-value {
                font-size: 14px;
            }

            .spider-value {
                font-size: 12px;
            }

            .stat-label, .spider-label {
                font-size: 10px;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr 1fr;
                gap: 6px;
            }

            .spider-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 5px;
            }

            .stat-card, .spider-card {
                min-height: 60px;
            }
        }

        /* 音乐播放器样式 */
        #musicbox {
            border: 1px solid #dae7ed;
            padding: 8px;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        #musicbox h3 {
            height: 20px;
            float: left;
            width: auto;
            margin-bottom: 10px;
            font-size: 14px;
            color: #333;
        }

        #music-player-container {
            clear: both;
            margin-top: 10px;
        }

        .music-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .control-btn.play-pause {
            width: 50px;
            height: 50px;
            font-size: 20px;
            background: rgba(255, 255, 255, 0.3);
        }

        .volume-control {
            display: flex;
            align-items: center;
            margin-left: 10px;
        }

        .volume-slider {
            width: 80px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            outline: none;
            cursor: pointer;
            -webkit-appearance: none;
        }

        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .volume-slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .music-info {
            margin-bottom: 15px;
        }

        .music-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            text-align: center;
            margin-bottom: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .music-progress {
            margin-bottom: 5px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e0e0e0;
            border-radius: 2px;
            overflow: hidden;
            cursor: pointer;
            margin-bottom: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #e91e63, #f06292);
            width: 0%;
            transition: width 0.1s ease;
        }

        .time-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
        }

        .music-list {
            border-top: 1px solid #eee;
            padding-top: 10px;
        }

        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
            font-weight: bold;
            color: #333;
        }

        .refresh-btn {
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 4px;
            border-radius: 3px;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #f0f0f0;
            color: #333;
        }

        .music-items {
            list-style: none;
            margin: 0;
            padding: 0;
            max-height: 200px;
            overflow-y: auto;
        }

        .music-items li {
            padding: 8px 10px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .music-items li:hover {
            background: #f8f9fa;
        }

        .music-items li.active {
            background: linear-gradient(135deg, #e91e63, #f06292);
            color: white;
        }

        .music-items li.loading-item {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .music-item-title {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 10px;
        }

        .music-item-play {
            color: #e91e63;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .music-items li:hover .music-item-play {
            opacity: 1;
        }

        .music-items li.active .music-item-play {
            color: white;
            opacity: 1;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .music-controls {
                gap: 8px;
                padding: 8px;
            }

            .control-btn {
                width: 35px;
                height: 35px;
                font-size: 14px;
            }

            .control-btn.play-pause {
                width: 45px;
                height: 45px;
                font-size: 18px;
            }

            .music-title {
                font-size: 13px;
            }

            .music-items {
                max-height: 150px;
            }

            .music-items li {
                padding: 6px 8px;
                font-size: 11px;
            }
        }
    </style>

    <script>
    // 数据归档月份切换功能
    document.addEventListener('DOMContentLoaded', function() {
        // 默认加载当前月份（7月）的数据
        loadArchiveData('2025', '07');

        // 数据归档自动滚动控制变量
        let archiveAutoSwitchTimer = null;
        let currentArchiveIndex = 0;
        let isArchiveHovering = false;
        const archiveLinks = document.querySelectorAll('.archive-month-link');

        // 绑定月份链接点击事件
        archiveLinks.forEach(function(link, index) {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                const year = this.getAttribute('data-year');
                const month = this.getAttribute('data-month');

                // 更新活动状态
                archiveLinks.forEach(function(l) {
                    l.classList.remove('active');
                });
                this.classList.add('active');

                // 更新当前索引
                currentArchiveIndex = index;

                // 加载对应月份的数据
                loadArchiveData(year, month);

                // 重置自动切换
                resetArchiveAutoSwitch();
            });

            // 鼠标悬停时停止自动切换
            link.addEventListener('mouseenter', function() {
                isArchiveHovering = true;
                clearTimeout(archiveAutoSwitchTimer);
            });

            // 鼠标离开时恢复自动切换
            link.addEventListener('mouseleave', function() {
                isArchiveHovering = false;
                startArchiveAutoSwitch();
            });
        });

        // 启动自动切换
        startArchiveAutoSwitch();

        function startArchiveAutoSwitch() {
            if (isArchiveHovering) return;

            archiveAutoSwitchTimer = setTimeout(function() {
                if (!isArchiveHovering && archiveLinks.length > 1) {
                    // 添加滚动效果
                    const currentLink = archiveLinks[currentArchiveIndex];
                    currentLink.classList.add('scrolling');

                    // 切换到下一个月份
                    currentArchiveIndex = (currentArchiveIndex + 1) % archiveLinks.length;
                    const nextLink = archiveLinks[currentArchiveIndex];

                    // 延迟一点时间显示滚动效果
                    setTimeout(function() {
                        currentLink.classList.remove('scrolling');
                        nextLink.click();
                    }, 300);
                }
            }, 5000); // 5秒自动切换
        }

        function resetArchiveAutoSwitch() {
            clearTimeout(archiveAutoSwitchTimer);
            startArchiveAutoSwitch();
        }
    });

    function loadArchiveData(year, month) {
        const loadingEl = document.getElementById('archive-loading');
        const listEl = document.getElementById('archive-list');
        const errorEl = document.getElementById('archive-error');

        // 直接隐藏加载状态和错误状态，不显示加载过渡
        loadingEl.style.display = 'none';
        errorEl.style.display = 'none';
        listEl.style.display = 'block';

        // 构造请求URL
        const url = '?mod=archives&date=' + year + month + '&ajax=1';

        // 发送AJAX请求
        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.websites) {
                    displayWebsites(data.websites);
                } else {
                    showError('暂无数据');
                }
            })
            .catch(error => {
                console.error('加载失败:', error);
                showError('加载失败，请稍后重试');
            });
    }

    function displayWebsites(websites) {
        const listEl = document.getElementById('archive-list');

        if (websites.length === 0) {
            showError('该月份暂无收录网站');
            return;
        }

        // 限制只显示前12条数据
        const limitedWebsites = websites.slice(0, 12);

        let html = '';
        limitedWebsites.forEach(function(site) {
            // 提取域名用于favicon
            let domain = '';
            try {
                const url = new URL(site.web_url.startsWith('http') ? site.web_url : 'http://' + site.web_url);
                domain = url.hostname;
            } catch (e) {
                domain = site.web_url.replace(/^https?:\/\//, '').split('/')[0];
            }

            html += '<li>';
            html += '<span>' + site.web_ctime + '</span>';
            html += '<a href="' + site.web_link + '" title="' + site.web_name + ' - ' + site.web_url + '">';
            html += '<img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://' + domain + '" width="18" height="18" />';
            html += site.web_name + ' - <small>' + site.web_url + '</small>';
            // 如果是当天发表的网站，添加new图标
            if (site.is_today) {
                html += '<span class="new-icon">new</span>';
            }
            html += '</a>';
            html += '</li>';
        });

        listEl.innerHTML = html;
        listEl.style.display = 'block';
    }

    function showError(message) {
        const listEl = document.getElementById('archive-list');
        const errorEl = document.getElementById('archive-error');

        listEl.innerHTML = '';
        listEl.style.display = 'none';
        errorEl.textContent = message;
        errorEl.style.display = 'block';
    }

    // 音乐播放器功能
    class MusicPlayer {
        constructor() {
            this.audio = document.getElementById('audioPlayer');
            this.playBtn = document.getElementById('playBtn');
            this.prevBtn = document.getElementById('prevBtn');
            this.nextBtn = document.getElementById('nextBtn');
            this.volumeBtn = document.getElementById('volumeBtn');
            this.refreshBtn = document.getElementById('refreshList');
            this.progressFill = document.getElementById('progressFill');
            this.currentTimeEl = document.getElementById('currentTime');
            this.totalTimeEl = document.getElementById('totalTime');
            this.currentTitleEl = document.getElementById('currentTitle');
            this.musicListEl = document.getElementById('musicList');
            this.progressBar = document.querySelector('.progress-bar');

            this.playlist = [];
            this.currentIndex = 0;
            this.isPlaying = false;
            this.isMuted = false;

            this.init();
        }

        init() {
            this.bindEvents();
            this.loadPlaylist();
        }

        bindEvents() {
            // 播放/暂停按钮
            this.playBtn.addEventListener('click', () => this.togglePlay());

            // 上一首/下一首
            this.prevBtn.addEventListener('click', () => this.prevTrack());
            this.nextBtn.addEventListener('click', () => this.nextTrack());

            // 静音按钮
            this.volumeBtn.addEventListener('click', () => this.toggleMute());

            // 刷新列表
            this.refreshBtn.addEventListener('click', () => this.loadPlaylist());

            // 进度条点击
            this.progressBar.addEventListener('click', (e) => this.seekTo(e));

            // 音频事件
            this.audio.addEventListener('loadedmetadata', () => this.updateDuration());
            this.audio.addEventListener('timeupdate', () => this.updateProgress());
            this.audio.addEventListener('ended', () => this.nextTrack());
            this.audio.addEventListener('error', () => this.handleError());
        }

        async loadPlaylist() {
            try {
                this.musicListEl.innerHTML = '<li class="loading-item">正在加载音乐列表...</li>';

                const response = await fetch('?mod=ajaxget&type=music_list');
                const data = await response.json();

                if (data.success && data.music_list) {
                    this.playlist = data.music_list;
                    this.renderPlaylist();
                } else {
                    this.musicListEl.innerHTML = '<li class="loading-item">暂无音乐</li>';
                }
            } catch (error) {
                console.error('加载音乐列表失败:', error);
                this.musicListEl.innerHTML = '<li class="loading-item">加载失败</li>';
            }
        }

        renderPlaylist() {
            if (this.playlist.length === 0) {
                this.musicListEl.innerHTML = '<li class="loading-item">暂无音乐</li>';
                return;
            }

            const html = this.playlist.map((track, index) => `
                <li data-index="${index}" ${index === this.currentIndex ? 'class="active"' : ''}>
                    <span class="music-item-title" title="${track.title}">${track.title}</span>
                    <i class="fas fa-play music-item-play"></i>
                </li>
            `).join('');

            this.musicListEl.innerHTML = html;

            // 绑定列表项点击事件
            this.musicListEl.querySelectorAll('li[data-index]').forEach(item => {
                item.addEventListener('click', () => {
                    const index = parseInt(item.dataset.index);
                    this.playTrack(index);
                });
            });
        }

        playTrack(index) {
            if (index < 0 || index >= this.playlist.length) return;

            this.currentIndex = index;
            const track = this.playlist[index];

            // 处理不同类型的音乐链接
            let audioUrl = this.processAudioUrl(track.url);

            this.audio.src = audioUrl;
            this.currentTitleEl.textContent = track.title;

            // 更新列表显示
            this.musicListEl.querySelectorAll('li').forEach((item, i) => {
                item.classList.toggle('active', i === index);
            });

            this.audio.load();
            this.play();
        }

        processAudioUrl(url) {
            // 处理网易云音乐链接
            if (url.includes('music.163.com')) {
                // 提取歌曲ID并转换为外链格式
                const match = url.match(/id=(\d+)/);
                if (match) {
                    return `https://music.163.com/song/media/outer/url?id=${match[1]}`;
                }
            }

            // 处理QQ音乐链接
            if (url.includes('y.qq.com')) {
                // QQ音乐需要特殊处理，这里返回原链接
                return url;
            }

            // 处理酷狗音乐链接
            if (url.includes('kugou.com')) {
                return url;
            }

            // 对于直接的音频文件链接，直接返回
            if (url.match(/\.(mp3|m4a|wav|flac|aac|ogg)(\?.*)?$/i)) {
                return url;
            }

            // 默认返回原链接
            return url;
        }

        play() {
            this.audio.play().then(() => {
                this.isPlaying = true;
                this.playBtn.innerHTML = '<i class="fas fa-pause"></i>';
            }).catch(error => {
                console.error('播放失败:', error);
                this.handleError();
            });
        }

        pause() {
            this.audio.pause();
            this.isPlaying = false;
            this.playBtn.innerHTML = '<i class="fas fa-play"></i>';
        }

        togglePlay() {
            if (this.playlist.length === 0) {
                this.loadPlaylist();
                return;
            }

            if (this.audio.src === '') {
                this.playTrack(0);
                return;
            }

            if (this.isPlaying) {
                this.pause();
            } else {
                this.play();
            }
        }

        prevTrack() {
            const newIndex = this.currentIndex > 0 ? this.currentIndex - 1 : this.playlist.length - 1;
            this.playTrack(newIndex);
        }

        nextTrack() {
            const newIndex = this.currentIndex < this.playlist.length - 1 ? this.currentIndex + 1 : 0;
            this.playTrack(newIndex);
        }

        toggleMute() {
            if (this.isMuted) {
                this.audio.muted = false;
                this.volumeBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
                this.isMuted = false;
            } else {
                this.audio.muted = true;
                this.volumeBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
                this.isMuted = true;
            }
        }

        seekTo(e) {
            const rect = this.progressBar.getBoundingClientRect();
            const percent = (e.clientX - rect.left) / rect.width;
            this.audio.currentTime = percent * this.audio.duration;
        }

        updateProgress() {
            if (this.audio.duration) {
                const percent = (this.audio.currentTime / this.audio.duration) * 100;
                this.progressFill.style.width = percent + '%';
                this.currentTimeEl.textContent = this.formatTime(this.audio.currentTime);
            }
        }

        updateDuration() {
            this.totalTimeEl.textContent = this.formatTime(this.audio.duration);
        }

        formatTime(seconds) {
            if (isNaN(seconds)) return '0:00';
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return mins + ':' + (secs < 10 ? '0' : '') + secs;
        }

        handleError() {
            console.error('音频播放错误，当前歌曲:', this.playlist[this.currentIndex]);
            this.currentTitleEl.textContent = '播放失败，正在尝试下一首...';

            // 标记当前歌曲为失败
            if (this.playlist[this.currentIndex]) {
                this.playlist[this.currentIndex].failed = true;
            }

            // 更新播放按钮状态
            this.isPlaying = false;
            this.playBtn.innerHTML = '<i class="fas fa-play"></i>';

            // 2秒后自动播放下一首
            setTimeout(() => {
                this.nextTrack();
            }, 2000);
        }

        // 检查音频是否可以播放
        canPlayAudio(url) {
            return new Promise((resolve) => {
                const testAudio = new Audio();
                testAudio.addEventListener('canplaythrough', () => resolve(true));
                testAudio.addEventListener('error', () => resolve(false));
                testAudio.src = url;

                // 5秒超时
                setTimeout(() => resolve(false), 5000);
            });
        }
    }

    // 初始化音乐播放器
    document.addEventListener('DOMContentLoaded', function() {
        if (document.getElementById('audioPlayer')) {
            window.musicPlayer = new MusicPlayer();
        }
    });
    </script>
</head>

<body>
    <!-- SEO优化 - 结构化数据：面包屑导航 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "首页",
                "item": "{#$site_url#}"
            }
        ]
    }
    </script>

    <!-- SEO优化 - 结构化数据：组织信息 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "95目录网",
        "url": "{#$site_url#}",
        "logo": "{#$site_url#}logo.png",
        "description": "{#$site_description#}",
        "sameAs": [
            "{#$site_url#}"
        ]
    }
    </script>

{#include file="topbar.html"#}

<div id="wrapper">
    <!-- ================= 顶部区域（logo / 搜索 / 导航） ================= -->
    <header id="header" role="banner">
        <!-- ---------- LOGO + 搜索 ---------- -->
        <div id="topbox">
            <a href="{#$site_url#}" class="logo" title="{#$site_title#}" aria-label="95目录网首页"></a>

            <div id="sobox" role="search">
                <form name="sofrm" class="sofrm" method="get" action="" onSubmit="return rewrite_search()" role="search" aria-label="网站搜索">
                    <input name="mod"  type="hidden" value="search" />
                    <input name="type" type="hidden" id="type" value="name" />

                    <div id="selopt">
                        <div id="cursel">网站名称</div>
                        <ul id="options" role="listbox" aria-label="搜索类型选择">
                            <li><a href="javascript: void(0);" name="name" role="option">网站名称</a></li>
                            <li><a href="javascript: void(0);" name="url" role="option">网站地址</a></li>
                            <li><a href="javascript: void(0);" name="tags" role="option">TAG标签</a></li>
                            <li><a href="javascript: void(0);" name="intro" role="option">网站描述</a></li>
                            <li><a href="javascript: void(0);" name="article" role="option">文章关键词</a></li>
                        </ul>
                    </div>

                    <input name="query" type="text" class="sipt" id="query" placeholder="请输入搜索关键词" aria-label="搜索关键词输入框" autocomplete="off" />
                    <input type="submit" class="sbtn" value="搜 索" aria-label="执行搜索" />
                </form>
            </div>
        </div>

        <!-- ---------- 主导航 ---------- -->
        <nav id="navbox" role="navigation" aria-label="主导航菜单">
            <ul class="navbar" role="menubar">
        		<li role="none"><a href="?mod=index" role="menuitem" aria-label="返回网站首页"><i class="fas fa-home" aria-hidden="true"></i> 网站首页</a></li>
                        <li role="none"><a href="?mod=webdir" role="menuitem" aria-label="浏览网站目录"><i class="fas fa-folder" aria-hidden="true"></i> 网站目录</a></li>
                        <li role="none"><a href="?mod=vip_list" style="color: #ffd700;" role="menuitem" aria-label="查看VIP站点"><i class="fas fa-crown" aria-hidden="true"></i> VIP站点</a></li>
                        <li role="none"><a href="?mod=article" role="menuitem" aria-label="阅读站长资讯"><i class="fas fa-newspaper" aria-hidden="true"></i> 站长资讯</a></li>
                        <li role="none"><a href="?mod=weblink" role="menuitem" aria-label="友情链接交换"><i class="fas fa-link" aria-hidden="true"></i> 链接交换</a></li>
                        <li role="none"><a href="?mod=category" role="menuitem" aria-label="按分类浏览网站"><i class="fas fa-list" aria-hidden="true"></i> 分类浏览</a></li>
                        <li role="none"><a href="?mod=update" role="menuitem" aria-label="查看最新收录网站"><i class="fas fa-clock" aria-hidden="true"></i> 最新收录</a></li>
                        <li role="none"><a href="?mod=pending" role="menuitem" aria-label="查看待审核网站"><i class="fas fa-hourglass-half" aria-hidden="true"></i> 待审核</a></li>
                        <li role="none"><a href="?mod=archives" role="menuitem" aria-label="查看数据归档"><i class="fas fa-archive" aria-hidden="true"></i> 数据归档</a></li>
                        <li role="none"><a href="?mod=top" role="menuitem" aria-label="查看TOP排行榜"><i class="fas fa-trophy" aria-hidden="true"></i> TOP排行榜</a></li>
                        <li role="none"><a href="?mod=feedback" role="menuitem" aria-label="提交意见反馈"><i class="fas fa-comments" aria-hidden="true"></i> 意见反馈</a></li>
                        <li role="none"><a href="/wailian" role="menuitem" aria-label="外链发布工具"><i class="fas fa-anchor" aria-hidden="true"></i> 外链发布</a></li>
                        <!--<li role="none"><a href="http://my0713.com" role="menuitem"><i class="far fa-play-circle" aria-hidden="true"></i> 免费影视</a></li>-->
        	</ul>
        </nav>

        <!-- ---------- 公告与快捷链接 ---------- -->
        <div id="txtbox">
            <!-- 数据统计 -->
            <div class="count" style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; text-align: center; font-size: 14px;">
                数据统计：
                <b style="color: #008800;">{#$stat.category#}</b>个主题分类，
                <b style="color: #008800;">{#$stat.website#}</b>个优秀站点，
                <b style="color: #E94E77;">{#$stat.vip#}</b>个VIP站点，
                <b style="color: #28a745;">{#$stat.recommend#}</b>个推荐位，
                <b style="color: #ff6600;">{#$stat.apply#}</b>个待审站点，
                <b style="color: #dc3545;">{#$stat.blacklist#}</b>个黑名单，
                <b style="color: #008800;">{#$stat.article#}</b>篇站长资讯，
                <b style="color: #008800;">{#$stat.user#}</b>名优质会员
                <!-- SEO优化：链接质量统计 -->
                {#if isset($stat.linked) && isset($stat.unlinked)#}
                ，<b style="color: #28a745;">{#$stat.linked#}</b>个互链站点，
                <b style="color: #6c757d;">{#$stat.unlinked#}</b>个单向收录
                {#/if#}
            </div>
            <div class="site-notice" style="MARGIN: 3px auto 0px;">
                <ul id="lunbo" style="list-style-type: none; margin-top: 0px;font-size: 12px;">
                    <li>
                        <p>
                            一共收录 <b style="color: #008800;font: bold 16px Arial;">{#$stat.website#}</b> 个优秀站点，
                            其中VIP站点 <b style="color: #E94E77;font: bold 16px Arial;">{#$stat.vip#}</b> 个，
                            推荐位 <b style="color: #28a745;font: bold 16px Arial;">{#$stat.recommend#}</b> 个，
                            注册会员 <b style="color: #008800;font: bold 16px Arial;">{#$stat.user#}</b> 名，
                            <strong>提示：</strong><b style="color: #ff0000;">每日更新站点，轻松到首页</b>
                        </p>
                    </li>
                    <li>
                        <p>
                            <span style="color: #ff0000">
                                本网站目录提供网站快速收录服务，
                                <a href="tencent://message/?uin=3632094&Site=95目录分类&Menu=yes" target="_blank" rel="nofollow">vip席位30元/年（直链）推荐位10元/年（内链）</a>。
                                <strong><a href="tencent://message/?uin=3632094&Site=95目录分类&Menu=yes" target="_blank" rel="nofollow">我要上推荐</a></strong>
                                联系客服：
                                <a href="tencent://message/?uin=3632094&amp;Site=95目录分类&amp;Menu=yes" target="blank" rel="nofollow">
                                    <img border="0" alt="95目录分类" src="http://wpa.qq.com/pa?p=1:3632094:4">
                                </a>
                            </span>
                        </p>
                    </li>
                </ul>
            </div>

            <div class="link">
                快捷方式：
                <a href="{#$site_root#}member/?mod=website&act=add">网站提交</a> -
                <a href="{#$site_root#}member/?mod=article&act=add">软文投稿</a> -
                <a href="{#$site_root#}?mod=diypage&pid=1">帮助中心</a>
            </div>
        </div>
    </header>

    <div class="uzkoo">
        <a href="http://www.uzkoo.com" target="_blank" rel="noopener noreferrer" aria-label="提交全新分类目录站点">提交全新分类目录站点</a>
    </div>

    <!-- ================= VIP 推荐区 ================= -->
    <div id="inbox1" class="clearfix" aria-labelledby="vip-section-title">
        <h3 id="vip-section-title">
            <span class="vip-title">
                <img src="/public/images/viptq.png" width="80" height="20" alt="VIP推荐网站">
            </span>
            <button onclick="showDonatePopup()" class="donate-button"
                    style="float: right; background-color: #FF6600; color: #fff; border: none; padding: 6px 14px; font-size: 14px; font-weight: bold; border-radius: 4px; cursor: pointer; transition: background-color 0.3s ease; position: relative; overflow: hidden;"
                    aria-label="申请推荐位服务">
                我要上推荐 <span class="shine-effect"></span>
            </button>
        </h3>

        {#foreach from=get_websites(0, 12, true) item=quick#}
        <ul class="inlist1">
            <li style="position: relative;">
                <!-- 浏览量显示 -->
                <div class="view-count-badge" style="position: absolute; top: 2px; right: 2px; background: linear-gradient(45deg, #ff6b35, #ff8c42); color: white; font-size: 10px; font-weight: bold; padding: 2px 6px; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); z-index: 10;">
                    推广 {#if $quick.web_views > 0#}{#$quick.web_views#}{#else#}0{#/if#} 次
                </div>
                <a href="{#$quick.web_furl#}" target="_blank" title="VIP：{#$quick.web_name#}">
                    <img src="{#$quick.web_pic#}" width="110" height="35"
                         alt="{#$quick.web_name#}" class="thumb" />
                </a><br>
                <a href="{#$quick.web_furl#}" target="_blank" title="VIP：{#$quick.web_name#}"
                   style="text-decoration: none; color: #007bff; font-weight: bold; transition: color 0.3s;">
                    {#$quick.web_name#}
                    <svg t="1741858436151" class="icon" viewBox="0 0 2048 1024" version="1.1"
                         xmlns="http://www.w3.org/2000/svg" p-id="1496" width="18" height="18">
                        <path d="M128 0h1792c76.8 0 128 51.2 128 128v768c0 76.8-51.2 128-128 128H128C51.2 1024 0 972.8 0 896V128C0 64 51.2 0 128 0z"
                              fill="#FF6600" p-id="1497"></path>
                        <path d="M473.6 832c-25.6 0-51.2-25.6-64-76.8L192 179.2h166.4L512 576l243.2-396.8h179.2l-371.2 576c-38.4 64-64 76.8-89.6 76.8z
                                 m409.6-12.8L972.8 192h153.6l-89.6 627.2zM1856 422.4c-12.8 64-38.4 128-102.4 179.2-51.2 38.4-115.2 64-179.2 64h-268.8L1280
                                 819.2h-153.6l51.2-320h435.2c25.6 0 38.4-12.8 51.2-25.6 12.8-12.8 25.6-38.4 25.6-51.2 0-25.6 0-38.4-12.8-51.2-12.8-12.8
                                 -38.4-25.6-51.2-25.6h-435.2l128-166.4h320c64 0 128 25.6 166.4 64 51.2 64 64 128 51.2 179.2z"
                              fill="#FFFFFF" p-id="1498"></path>
                    </svg>
                </a>
            </li>
        </ul>
        {#/foreach#}
    </div>

    <!-- 打赏弹窗 -->
    <div id="donate-popup" style="display:none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div class="donate-popup-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; max-width: 500px; width: 90%;">
            <span class="close" onclick="closeDonatePopup()" style="position: absolute; top: 10px; right: 15px; font-size: 24px; cursor: pointer; color: #999;">&times;</span>
            <h3 style="color: #4A90E2; font-family: 'Arial', sans-serif; font-size: 22px; text-align: center; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2); margin-top: 0;">
                推荐服务价格表
            </h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p style="margin: 0; line-height: 1.6; text-align: center;">
                    <strong style="color: #28a745;">10元上推荐位</strong> - 首页推荐展示<br>
                    <strong style="color: #E94E77;">VIP直链席位30元/每年</strong> - 顶部VIP位置<br>
                    <strong style="color: #ff6600;">5元快审服务</strong> - 1-3个工作日审核
                </p>
            </div>
            <p style="text-align: center; margin: 15px 0; color: #666;">
                备注格式：<strong style="color: #F39C12;">推荐/vip/快审+网址</strong>
            </p>
            <div class="donate-qr-codes" style="display: flex; justify-content: space-around; margin: 20px 0;">
                <div style="text-align: center;">
                    <h4>微信支付</h4>
                    <img src="/uploads/article/weixin.jpg" alt="WeChat QR Code" style="width: 150px; height: 150px;">
                </div>
                <div style="text-align: center;">
                    <h4>支付宝支付</h4>
                    <img src="/uploads/article/zhifubao.jpg" alt="Alipay QR Code" style="width: 150px; height: 150px;">
                </div>
            </div>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
                <h4 style="margin-top: 0; color: #333;">服务说明：</h4>
                <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                    <li>推荐位：展示在首页推荐区域</li>
                    <li>VIP位：展示在顶部VIP推广区</li>
                    <li>快审：1-3个工作日完成审核</li>
                    <li>付款后请联系客服提供网站信息</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="blank10"></div>

    <!-- ================= 主体：左栏 ================= -->
    <div id="homebox">
        <div id="homebox-left">
            <!-- ========== 实用工具列表 ========== -->
            <dl id="hcatebox" class="clearfix" aria-labelledby="tools-title">
                <dt id="tools-title"><a href="/" aria-label="实用工具列表"><i class="fas fa-tools" style="color: #28a745; margin-right: 5px;" aria-hidden="true"></i>实用工具</a></dt>
                <dd>
                    <ul class="hcatelist" role="list" aria-label="实用工具链接">
                        <li><a href="https://www.12306.cn" target="_blank" rel="nofollow noopener" aria-label="12306火车票预订">火 车 票</a></li>
                        <li><a href="https://tianqi.qq.com" target="_blank" rel="nofollow noopener" aria-label="腾讯天气预报">天气预报</a></li>
                        <li><a href="https://www.rili.com.cn" target="_blank" rel="nofollow noopener" aria-label="万年历查询">万 年 历</a></li>
                        <li><a href="https://flights.ctrip.com/fuzzysearch/search" target="_blank" rel="nofollow noopener" aria-label="携程特价机票">特价机票</a></li>
                        <li><a href="http://typhoon.nmc.cn/mobile.html" target="_blank" rel="nofollow noopener" aria-label="台风路径实时查询">台风路径</a></li>
                        <li><a href="https://www.kuaidi100.com" target="_blank" rel="nofollow noopener" aria-label="快递100查询">快递查询</a></li>
                        <li><a href="https://www.8684.cn" target="_blank" rel="nofollow noopener" aria-label="8684公交线路查询">公交线路</a></li>
                        <li><a href="https://summary.jrj.com.cn/hsMarket" target="_blank" rel="nofollow noopener" aria-label="金融界股票行情">股票行情</a></li>
                        <li><a href="https://dey.11185.cn/web/#/idtoolkitAddress" target="_blank" rel="nofollow noopener" aria-label="邮政编码查询">邮编查询</a></li>
                        <li><a href="https://www.boc.cn/sourcedb/whpj/" target="_blank" rel="nofollow noopener" aria-label="中国银行外汇牌价">外汇牌价</a></li>
                        <li><a href="https://www.cwl.gov.cn/ygkj/wqkjgg/ssq/" target="_blank" rel="nofollow noopener" aria-label="福利彩票开奖结果">福利彩票</a></li>
                        <li><a href="https://bj.122.gov.cn" target="_blank" rel="nofollow noopener" aria-label="交通违章查询">违章查询</a></li>
                        <li><a href="https://map.baidu.com" target="_blank" rel="nofollow noopener" aria-label="百度地图导航">百度地图</a></li>
                        <li><a href="https://fanyi.baidu.com" target="_blank" rel="nofollow noopener" aria-label="百度在线翻译">在线翻译</a></li>
                        <li><a href="https://www.speedtest.cn" target="_blank" rel="nofollow noopener" aria-label="网络测速工具">网速测试</a></li>
                    </ul>
                </dd>
            </dl>

            <div class="blank10"></div>

            <!-- ========== 按地区搜索 ========== -->
            <dl id="hcatebox" class="clearfix" aria-labelledby="province-tags-title">
                <dt id="province-tags-title">
                    <a href="javascript:void(0);" aria-label="按地区搜索">
                        <i class="fas fa-map-marker-alt" style="color: #28a745; margin-right: 5px;" aria-hidden="true"></i>按地区搜索
                    </a>
                </dt>
                <dd>
                    <ul class="hcatelist province-tags-list" role="list" aria-label="中国省份地区标签">
                        <!-- 直辖市 -->
                        <li><a href="javascript:void(0);" class="province-tag municipality" data-province="北京" role="button" tabindex="0" aria-label="搜索北京地区网站">北京</a></li>
                        <li><a href="javascript:void(0);" class="province-tag municipality" data-province="上海" role="button" tabindex="0" aria-label="搜索上海地区网站">上海</a></li>
                        <li><a href="javascript:void(0);" class="province-tag municipality" data-province="天津" role="button" tabindex="0" aria-label="搜索天津地区网站">天津</a></li>
                        <li><a href="javascript:void(0);" class="province-tag municipality" data-province="重庆" role="button" tabindex="0" aria-label="搜索重庆地区网站">重庆</a></li>

                        <!-- 省份 -->
                        <li><a href="javascript:void(0);" class="province-tag" data-province="河北" role="button" tabindex="0" aria-label="搜索河北地区网站">河北</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="山西" role="button" tabindex="0" aria-label="搜索山西地区网站">山西</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="辽宁" role="button" tabindex="0" aria-label="搜索辽宁地区网站">辽宁</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="吉林" role="button" tabindex="0" aria-label="搜索吉林地区网站">吉林</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="黑龙江" role="button" tabindex="0" aria-label="搜索黑龙江地区网站">黑龙江</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="江苏" role="button" tabindex="0" aria-label="搜索江苏地区网站">江苏</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="浙江" role="button" tabindex="0" aria-label="搜索浙江地区网站">浙江</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="安徽" role="button" tabindex="0" aria-label="搜索安徽地区网站">安徽</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="福建" role="button" tabindex="0" aria-label="搜索福建地区网站">福建</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="江西" role="button" tabindex="0" aria-label="搜索江西地区网站">江西</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="山东" role="button" tabindex="0" aria-label="搜索山东地区网站">山东</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="河南" role="button" tabindex="0" aria-label="搜索河南地区网站">河南</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="湖北" role="button" tabindex="0" aria-label="搜索湖北地区网站">湖北</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="湖南" role="button" tabindex="0" aria-label="搜索湖南地区网站">湖南</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="广东" role="button" tabindex="0" aria-label="搜索广东地区网站">广东</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="海南" role="button" tabindex="0" aria-label="搜索海南地区网站">海南</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="四川" role="button" tabindex="0" aria-label="搜索四川地区网站">四川</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="贵州" role="button" tabindex="0" aria-label="搜索贵州地区网站">贵州</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="云南" role="button" tabindex="0" aria-label="搜索云南地区网站">云南</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="陕西" role="button" tabindex="0" aria-label="搜索陕西地区网站">陕西</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="甘肃" role="button" tabindex="0" aria-label="搜索甘肃地区网站">甘肃</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="青海" role="button" tabindex="0" aria-label="搜索青海地区网站">青海</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="台湾" role="button" tabindex="0" aria-label="搜索台湾地区网站">台湾</a></li>

                        <!-- 自治区 -->
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="内蒙古" role="button" tabindex="0" aria-label="搜索内蒙古地区网站">内蒙古</a></li>
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="广西" role="button" tabindex="0" aria-label="搜索广西地区网站">广西</a></li>
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="西藏" role="button" tabindex="0" aria-label="搜索西藏地区网站">西藏</a></li>
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="宁夏" role="button" tabindex="0" aria-label="搜索宁夏地区网站">宁夏</a></li>
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="新疆" role="button" tabindex="0" aria-label="搜索新疆地区网站">新疆</a></li>

                        <!-- 特别行政区 -->
                        <li><a href="javascript:void(0);" class="province-tag special" data-province="香港" role="button" tabindex="0" aria-label="搜索香港地区网站">香港</a></li>
                        <li><a href="javascript:void(0);" class="province-tag special" data-province="澳门" role="button" tabindex="0" aria-label="搜索澳门地区网站">澳门</a></li>
                    </ul>
                </dd>
            </dl>

            <div class="blank10"></div>

            <!-- ========== 分类目录（动态） ========== -->
            <dl id="hcatebox" class="clearfix" aria-label="网站分类目录">
                {#foreach from=get_categories() item=cate#}
                {#if $cate.cate_mod == 'webdir'#}
                <dt><a href="{#$cate.cate_link#}" aria-label="查看{#$cate.cate_name#}分类">{#$cate.cate_name#}</a></dt>
                <dd>
                    <ul class="hcatelist" role="list" aria-label="{#$cate.cate_name#}子分类">
                        {#foreach from=get_categories($cate.cate_id) item=scate#}
                        <li><a href="{#$scate.cate_link#}" aria-label="查看{#$scate.cate_name#}网站">{#$scate.cate_name#}</a></li>
                        {#/foreach#}
                    </ul>
                </dd>
                {#/if#}
                {#/foreach#}
            </dl>

            <div class="blank10"></div>

            <!-- ========== 最新收录 ========== -->
            <div id="newbox" aria-labelledby="latest-sites-title">
                <h3 id="latest-sites-title"><i class="fas fa-plus-circle" style="color: #007bff; margin-right: 5px;" aria-hidden="true"></i>最新收录</h3>
                <ul class="newlist" role="list" aria-label="最新收录网站列表">
                    {#foreach from=get_websites(0, 14) item=new#}
                    <li>
                        <span>{#$new.web_ctime#}</span>
                        <a href="{#$new.web_link#}" title="{#$new.web_name#} - {#$new.web_url#}" aria-label="访问{#$new.web_name#}网站">
                            <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$new.web_url#}"
                                 width="18" height="18" alt="{#$new.web_name#}网站图标" loading="lazy" />{#$new.web_name#} - <small>{#$new.web_url#}</small>
                            {#if $new.is_today#}<span class="new-icon" aria-label="今日新增">new</span>{#/if#}
                        </a>
                    </li>
                    {#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- ========== 待审核 ========== -->
            <div id="newbox">
                <h3><i class="fas fa-hourglass-half" style="color: #ffc107; margin-right: 5px;"></i>待审核</h3>
                <ul class="newlist">
                    {#foreach from=get_pending_websites(14) item=pending#}
                    <li>
                        <span>{#$pending.web_ctime#}</span>
                        <a href="{#$pending.web_link#}" title="{#$pending.web_name#} - 待审核网站">
                            <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$pending.web_url#}"
                                 width="18" height="18" />{#$pending.web_name#} - <small style="color: #ff6600;">待审核</small>
                            {#if $pending.is_today#}<span class="new-icon">new</span>{#/if#}
                        </a>
                    </li>
                    {#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- ========== 黑名单 ========== -->
            <div id="newbox">
                <h3><i class="fas fa-ban" style="color: #dc3545; margin-right: 5px;"></i>黑名单</h3>
                <ul class="newlist">
                    {#foreach from=get_blacklist_websites(14) item=blacklist#}
                    <li>
                        <span>{#$blacklist.web_ctime#}</span>
                        <a href="{#$blacklist.web_link#}" title="{#$blacklist.web_name#} - 黑名单网站">
                            <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$blacklist.web_url#}"
                                 width="18" height="18" />{#$blacklist.web_name#} - <small style="color: #333;">黑名单</small>
                            {#if $blacklist.is_today#}<span class="new-icon">new</span>{#/if#}
                        </a>
                    </li>
                    {#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- ========== 数据归档 ========== -->
            <div id="newbox">
                <h3><i class="fas fa-archive" style="color: #6f42c1; margin-right: 5px;"></i>数据归档</h3>
                <ul class="arcbox-list">
                    {#foreach from=get_archives() key=year item=arr#}
                    <li>
                        <b>{#$year#}年</b>
                        {#foreach from=$arr key=month item=item#}
                        <a href="javascript:void(0);"
                           class="archive-month-link {#if $year == '2025' && $month == '07'#}active{#/if#}"
                           data-year="{#$year#}"
                           data-month="{#$month#}"
                           title="{#$year#}年{#$month#}月共收录{#$item.site_count#}个优秀站点">
                            {#$month#}月
                        </a>
                        {#/foreach#}
                    </li>
                    {#/foreach#}
                </ul>

                <!-- 网站列表显示区域 - 完全按照最新收录的结构 -->
                <div class="archive-content-container">
                    <div id="archive-loading" class="archive-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                    </div>
                    <ul id="archive-list" class="newlist">
                        <!-- 网站列表将通过AJAX加载，结构与最新收录完全一致 -->
                    </ul>
                    <div id="archive-error" class="archive-error" style="display: none;">
                        加载失败，请稍后重试
                    </div>
                </div>
            </div>
        </div><!-- /homebox-left -->

        <!-- ================= 右侧区域 ================= -->
        <div id="homebox-right" role="complementary" aria-label="推荐内容和导航">
            <!-- 站长推荐 -->
            <div id="bestbox" aria-labelledby="recommend-title">
                <h3 id="recommend-title"><span style="color: #07c; font-weight: bold; text-shadow: 0 1px 2px rgba(255, 215, 0, 0.3); font-size: 14px;"><i class="fas fa-crown" style="color: #ffd700; margin-right: 5px;" aria-hidden="true"></i>站长推荐</span></h3>
                <ul class="clearfix bestlist-enhanced" role="list" aria-label="站长推荐网站列表">
                    {#foreach from=get_websites(0, 35, false, true) item=best#}
                    <li class="recommend-item">
                        <a href="/go.php?url=http://{#$best.web_url#}" title="{#$best.web_name#}" class="recommend-link" aria-label="访问推荐网站{#$best.web_name#}">
                            <div class="recommend-icon">
                                <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$best.web_url#}"
                                     width="28" height="28" alt="{#$best.web_name#}网站图标" loading="lazy" />
                                <div class="recommend-badge" aria-label="推荐标识">推荐</div>
                            </div>
                            <span class="recommend-name">{#$best.web_name#}</span>
                        </a>
                    </li>
                    {#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- 在线音乐播放器 -->
            <div id="musicbox" class="clearfix" aria-labelledby="music-player-title">
                <h3 id="music-player-title">
                    <span style="color: #e91e63; font-weight: bold; text-shadow: 0 1px 2px rgba(233, 30, 99, 0.3); font-size: 14px;">
                        <i class="fas fa-music" style="color: #e91e63; margin-right: 5px;" aria-hidden="true"></i>在线音乐
                    </span>
                </h3>
                <div id="music-player-container">
                    <!-- 音乐播放器控制面板 -->
                    <div class="music-controls">
                        <button id="prevBtn" class="control-btn" title="上一首">
                            <i class="fas fa-step-backward"></i>
                        </button>
                        <button id="playBtn" class="control-btn play-pause" title="播放/暂停">
                            <i class="fas fa-play"></i>
                        </button>
                        <button id="nextBtn" class="control-btn" title="下一首">
                            <i class="fas fa-step-forward"></i>
                        </button>
                        <button id="volumeBtn" class="control-btn" title="静音/取消静音">
                            <i class="fas fa-volume-up"></i>
                        </button>
                        <div class="volume-control">
                            <input type="range" id="volumeSlider" min="0" max="100" value="50" class="volume-slider" title="音量控制">
                        </div>
                    </div>

                    <!-- 当前播放信息 -->
                    <div class="music-info">
                        <div class="music-title" id="currentTitle">选择一首歌曲开始播放</div>
                        <div class="music-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <div class="time-info">
                                <span id="currentTime">0:00</span>
                                <span id="totalTime">0:00</span>
                            </div>
                        </div>
                    </div>

                    <!-- 音乐列表 -->
                    <div class="music-list">
                        <div class="list-header">
                            <span>播放列表</span>
                            <button id="refreshList" class="refresh-btn" title="刷新列表">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <ul id="musicList" class="music-items">
                            <!-- 音乐列表将通过JavaScript动态加载 -->
                            <li class="loading-item">正在加载音乐列表...</li>
                        </ul>
                    </div>

                    <!-- 隐藏的音频元素 -->
                    <audio id="audioPlayer" preload="none"></audio>
                </div>
            </div>

            <div class="blank10"></div>

            <!-- 酷站导航 -->
            <div id="coolbox" class="clearfix">
                <h3><i class="fas fa-star" style="color: #ffd700; margin-right: 5px;"></i>酷站导航</h3>
                <ul class="csitelist">
                    {#foreach from=get_best_categories() item=cate name=csite#}
                    <li>
                        <h4><a href="{#$cate.cate_link#}">{#$cate.cate_name#}</a></h4>
                        <a href="{#$cate.cate_link#}" class="more">更多>></a>
                        {#foreach from=get_websites($cate.cate_id, 8) item=cool#}
                        <span>
                            <a href="{#$cool.web_link#}" title="{#$cool.web_name#}">
                                <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$cool.web_url#}"
                                     width="18" height="18" />{#$cool.web_name#}
                            </a>
                        </span>
                        {#/foreach#}
                    </li>
                    {#if $smarty.foreach.csite.iteration % 5 == 0 && $smarty.foreach.csite.iteration != 20#}
                    <li class="sline"></li>
                    {#/if#}
                    {#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- 站点资讯 + 链接交换 -->
            <div id="rowbox" class="clearfix">
                <div id="newsbox">
                    <h3><i class="fas fa-newspaper" style="color: #17a2b8; margin-right: 5px;"></i>站点资讯</h3>
                    <span1><a href="?mod=article" class="more">更多>></a></span1>

                    <!-- 文章分类导航 -->
                    <div class="article-categories">
                        <a href="javascript:void(0);" class="article-cate-link active" data-cate-id="0">最新</a>
                        {#foreach from=get_categories() item=cate#}
                        {#if $cate.cate_mod == 'article' && $cate.cate_postcount > 0#}
                        <a href="javascript:void(0);" class="article-cate-link" data-cate-id="{#$cate.cate_id#}">{#$cate.cate_name#}</a>
                        {#/if#}
                        {#/foreach#}
                    </div>

                    <!-- 文章列表 -->
                    <div class="article-content-container">
                        <ul id="article-list" class="newslist">
                            {#foreach from=get_articles(0, 10, false) item=art#}
                            <li data-number="{#$idx+1#}">
                                <span>{#$art.art_ctime#}</span>
                                <a href="{#$art.art_link#}" title="{#$art.art_title#}">
                                    {#$art.art_title#}
                                    {#if $art.is_today#}<span class="new-icon">new</span>{#/if#}
                                </a>
                            </li>
                            {#/foreach#}
                        </ul>
                        <div id="article-error" style="display: none; text-align: center; padding: 10px; color: #d9534f;">
                            暂无文章
                        </div>
                    </div>
                </div>

                <div class="line"></div>

                <div id="exlink">
                    <h3><i class="fas fa-handshake" style="color: #fd7e14; margin-right: 5px;"></i>链接交换</h3>
                    <ul class="exlist">
                        {#foreach from=get_weblinks(0, 8) item=link#}
                        <li>
                            <a href="{#$link.web_link#}">
                                <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$link.web_url#}" width="18" height="18" />
                                {#$link.link_name#} - <img src="module/dr_badge.php?domain={#$link.web_url#}&size=small" alt="DR Badge" style="vertical-align: text-top; margin-top: -1px;" />，
                                百度权重{#$link.web_brank#}，{#$link.deal_type#}友情链接
                            </a>
                        </li>
                        {#/foreach#}
                    </ul>
                </div>
            </div>
        </div><!-- /homebox-right -->
    </div><!-- /homebox -->

    <div class="blank10"></div>

    <!-- ================= 最新点入 / 出 / 友情链接 ================= -->
    <div id="inbox" class="clearfix" aria-labelledby="latest-visits-title">
        <h3 id="latest-visits-title"><i class="fas fa-sign-in-alt" style="color: #28a745; margin-right: 5px;" aria-hidden="true"></i>最新点入</h3>
        <ul class="inlist" role="list" aria-label="最新点入网站列表">
            {#nocache#}
            {#foreach from=get_websites(0, 36, false, false, 'instat') item=instat#}
            <li>
                <a href="{#$instat.web_link#}" title="{#$instat.web_name#}" aria-label="访问{#$instat.web_name#}">
                    <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$instat.web_url#}"
                         width="16" height="16" alt="{#$instat.web_name#}图标" loading="lazy" />{#$instat.web_name#}
                </a>
            </li>
            {#/foreach#}
            {#/nocache#}
        </ul>
    </div>

    <div class="blank10"></div>

    <div id="inbox" class="clearfix" aria-labelledby="latest-outbound-title">
        <h3 id="latest-outbound-title"><i class="fas fa-sign-out-alt" style="color: #dc3545; margin-right: 5px;" aria-hidden="true"></i>最新点出</h3>
        <ul class="inlist" role="list" aria-label="最新点出网站列表">
            {#nocache#}
            {#foreach from=get_websites(0, 36, false, false, 'outstat') item=outstat#}
            <li>
                <a href="{#$outstat.web_link#}" title="{#$outstat.web_name#}" aria-label="访问{#$outstat.web_name#}">
                    <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$outstat.web_url#}"
                         width="16" height="16" alt="{#$outstat.web_name#}图标" loading="lazy" />{#$outstat.web_name#}
                </a>
            </li>
            {#/foreach#}
            {#/nocache#}
        </ul>
    </div>

    <div class="blank10"></div>

    <div id="inbox" class="clearfix" aria-labelledby="friend-links-title">
        <h3 id="friend-links-title"><i class="fas fa-heart" style="color: #e91e63; margin-right: 5px;" aria-hidden="true"></i>友情链接</h3>
        <ul class="inlist" role="list" aria-label="友情链接列表">
            {#foreach from=get_links() item=link#}
            <li>
                <a href="{#$link.link_url#}" target="_blank" rel="noopener noreferrer" title="{#$link.link_name#}" aria-label="访问友情链接{#$link.link_name#}">
                    <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url={#$link.link_url#}"
                         width="16" height="16" alt="{#$link.link_name#}图标" loading="lazy" />{#$link.link_name#}
                </a>
            </li>
            {#/foreach#}
        </ul>
    </div>


    <div class="blank10"></div>

    <!-- 今日访问统计 -->
    <div id="todayStatsBox" class="clearfix">
        <h3>📊 今日访问统计</h3>
        <div class="stats-grid">
            <div class="stat-card" data-tooltip="今日网站总访问量">
                <div class="stat-icon">👁️</div>
                <div class="stat-label">总浏览</div>
                <div class="stat-value" id="todayTotalVisits">加载中...</div>
            </div>
            <div class="stat-card" data-tooltip="今日站点页面访问量">
                <div class="stat-icon">🏠</div>
                <div class="stat-label">站点浏览</div>
                <div class="stat-value" id="todaySiteVisits">加载中...</div>
            </div>
            <div class="stat-card" data-tooltip="今日文章页面访问量">
                <div class="stat-icon">📄</div>
                <div class="stat-label">文章浏览</div>
                <div class="stat-value" id="todayArticleVisits">加载中...</div>
            </div>
            <div class="stat-card" data-tooltip="今日用户点击外链次数">
                <div class="stat-icon">🔗</div>
                <div class="stat-label">出站次数</div>
                <div class="stat-value" id="todayOutlinks">加载中...</div>
            </div>
        </div>
    </div>

    <!-- 当日蜘蛛统计 -->
    <div class="blank10"></div>
    <div id="spiderStatsBox" class="clearfix">
        <h3>🕷️ 当日蜘蛛统计</h3>
        <div class="spider-grid">
            <div class="spider-card" data-tooltip="Google搜索引擎爬虫访问次数">
                <div class="spider-icon" style="color: #4285f4;">G</div>
                <div class="spider-label">Google</div>
                <div class="spider-value" id="spiderGoogle">0</div>
            </div>
            <div class="spider-card" data-tooltip="百度搜索引擎爬虫访问次数">
                <div class="spider-icon" style="color: #2932e1;">百</div>
                <div class="spider-label">Baidu</div>
                <div class="spider-value" id="spiderBaidu">0</div>
            </div>
            <div class="spider-card" data-tooltip="Bing搜索引擎爬虫访问次数">
                <div class="spider-icon" style="color: #008373;">B</div>
                <div class="spider-label">Bing</div>
                <div class="spider-value" id="spiderBing">0</div>
            </div>
            <div class="spider-card" data-tooltip="搜狗搜索引擎爬虫访问次数">
                <div class="spider-icon" style="color: #fb6c00;">搜</div>
                <div class="spider-label">Sogou</div>
                <div class="spider-value" id="spiderSogou">0</div>
            </div>
            <div class="spider-card" data-tooltip="360搜索引擎爬虫访问次数">
                <div class="spider-icon" style="color: #01c101;">360</div>
                <div class="spider-label">360</div>
                <div class="spider-value" id="spiderSo360">0</div>
            </div>
            <div class="spider-card" data-tooltip="字节跳动搜索爬虫访问次数">
                <div class="spider-icon" style="color: #ff3040;">字</div>
                <div class="spider-label">Byte</div>
                <div class="spider-value" id="spiderBytedance">0</div>
            </div>
        </div>
    </div>

    <div class="blank10"></div>

    {#include file="footer.html"#}
</div><!-- /wrapper -->

<!-- SEO优化 - 结构化数据：网站导航 -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "SiteNavigationElement",
    "name": "网站导航",
    "url": "{#$site_url#}",
    "hasPart": [
        {
            "@type": "SiteNavigationElement",
            "name": "网站目录",
            "url": "{#$site_url#}?mod=webdir"
        },
        {
            "@type": "SiteNavigationElement",
            "name": "VIP站点",
            "url": "{#$site_url#}?mod=vip_list"
        },
        {
            "@type": "SiteNavigationElement",
            "name": "站长资讯",
            "url": "{#$site_url#}?mod=article"
        },
        {
            "@type": "SiteNavigationElement",
            "name": "链接交换",
            "url": "{#$site_url#}?mod=weblink"
        },
        {
            "@type": "SiteNavigationElement",
            "name": "最新收录",
            "url": "{#$site_url#}?mod=update"
        }
    ]
}
</script>

<!-- SEO优化 - 结构化数据：网站统计信息 -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Dataset",
    "name": "95目录网站统计",
    "description": "95目录网收录的网站统计数据",
    "url": "{#$site_url#}",
    "keywords": ["网站目录", "网站收录", "网站分类", "站长工具"],
    "creator": {
        "@type": "Organization",
        "name": "95目录网"
    }
}
</script>

<script>
// 统一弹窗功能
function showDonatePopup() {
    document.getElementById('donate-popup').style.display = 'block';
}

function closeDonatePopup() {
    document.getElementById('donate-popup').style.display = 'none';
}

// 点击弹窗外部关闭
document.addEventListener('DOMContentLoaded', function() {
    const donatePopup = document.getElementById('donate-popup');
    if (donatePopup) {
        donatePopup.addEventListener('click', function(e) {
            if (e.target === this) {
                closeDonatePopup();
            }
        });
    }

    // 初始化站点资讯分类切换功能
    initArticleSwitch();

    // 初始化省份标签搜索功能
    initProvinceTagsSearch();
});

// ESC键关闭弹窗
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDonatePopup();
    }
});

// 站点资讯分类切换功能
function initArticleSwitch() {
    let autoSwitchTimer = null;
    let currentCateIndex = 0;
    let isHovering = false;

    // 获取所有分类链接
    const cateLinks = document.querySelectorAll('.article-cate-link');

    console.log('文章分类链接数量:', cateLinks.length); // 调试信息

    if (cateLinks.length === 0) return;

    // 默认加载最新文章
    loadArticleData(0);

    // 绑定分类链接点击事件
    cateLinks.forEach(function(link, index) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const cateId = this.getAttribute('data-cate-id');

            // 更新活动状态
            cateLinks.forEach(function(l) {
                l.classList.remove('active');
            });
            this.classList.add('active');

            // 更新当前索引
            currentCateIndex = index;

            // 加载对应分类的文章
            loadArticleData(cateId);

            // 重置自动切换
            resetAutoSwitch();
        });

        // 鼠标悬停时停止自动切换
        link.addEventListener('mouseenter', function() {
            isHovering = true;
            clearTimeout(autoSwitchTimer);
        });

        // 鼠标离开时恢复自动切换
        link.addEventListener('mouseleave', function() {
            isHovering = false;
            startAutoSwitch();
        });
    });

    // 启动自动切换
    startAutoSwitch();

    function startAutoSwitch() {
        if (isHovering) return;

        autoSwitchTimer = setTimeout(function() {
            if (!isHovering && cateLinks.length > 1) {
                // 添加滚动效果
                const currentLink = cateLinks[currentCateIndex];
                console.log('添加滚动效果到:', currentLink.textContent); // 调试信息
                currentLink.classList.add('scrolling');

                // 切换到下一个分类
                currentCateIndex = (currentCateIndex + 1) % cateLinks.length;
                const nextLink = cateLinks[currentCateIndex];

                // 延迟一点时间显示滚动效果
                setTimeout(function() {
                    currentLink.classList.remove('scrolling');
                    console.log('点击下一个链接:', nextLink.textContent); // 调试信息
                    nextLink.click();
                }, 300);
            }
        }, 5000); // 5秒自动切换
    }

    function resetAutoSwitch() {
        clearTimeout(autoSwitchTimer);
        startAutoSwitch();
    }
}

function loadArticleData(cateId) {
    const listEl = document.getElementById('article-list');
    const errorEl = document.getElementById('article-error');

    if (!listEl || !errorEl) return;

    // 直接隐藏错误状态，显示列表
    errorEl.style.display = 'none';
    listEl.style.display = 'block';

    // 构造请求URL
    const url = '?mod=article&cate_id=' + cateId + '&ajax=1&limit=10';

    // 发送AJAX请求
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.articles) {
                displayArticles(data.articles);
            } else {
                showArticleError('暂无文章');
            }
        })
        .catch(error => {
            console.error('加载失败:', error);
            showArticleError('加载失败，请稍后重试');
        });
}

function displayArticles(articles) {
    const listEl = document.getElementById('article-list');

    if (articles.length === 0) {
        showArticleError('该分类暂无文章');
        return;
    }

    let html = '';
    articles.forEach(function(article, index) {
        html += '<li data-number="' + (index + 1) + '">';
        html += '<span>' + article.art_ctime + '</span>';
        html += '<a href="' + article.art_link + '" title="' + article.art_title + '">';
        html += article.art_title;
        // 如果是当天发表的文章，添加new图标
        if (article.is_today) {
            html += '<span class="new-icon">new</span>';
        }
        html += '</a>';
        html += '</li>';
    });

    listEl.innerHTML = html;
    listEl.style.display = 'block';
}

function showArticleError(message) {
    const listEl = document.getElementById('article-list');
    const errorEl = document.getElementById('article-error');

    listEl.innerHTML = '';
    listEl.style.display = 'none';
    errorEl.textContent = message;
    errorEl.style.display = 'block';
}

// 省份标签搜索功能
function initProvinceTagsSearch() {
    const provinceTags = document.querySelectorAll('.province-tag');

    provinceTags.forEach(function(tag) {
        // 鼠标点击事件
        tag.addEventListener('click', function() {
            handleProvinceSearch(this);
        });

        // 键盘事件支持（Enter和Space键）
        tag.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleProvinceSearch(this);
            }
        });
    });
}

function handleProvinceSearch(tagElement) {
    const province = tagElement.getAttribute('data-province');

    if (!province) return;

    // 添加点击动画效果
    tagElement.classList.add('clicked');
    setTimeout(function() {
        tagElement.classList.remove('clicked');
    }, 300);

    // 执行搜索
    performProvinceSearch(province);
}

function performProvinceSearch(province) {
    // 构建搜索URL
    const searchUrl = '?mod=search&type=name&query=' + encodeURIComponent(province);

    // 跳转到搜索结果页面
    window.location.href = searchUrl;
}

// 为省份标签添加搜索提示功能
function addProvinceSearchTooltips() {
    const provinceTags = document.querySelectorAll('.province-tag');

    provinceTags.forEach(function(tag) {
        const province = tag.getAttribute('data-province');
        tag.title = '点击搜索' + province + '地区的网站';
    });
}

// 页面加载完成后添加提示
document.addEventListener('DOMContentLoaded', function() {
    addProvinceSearchTooltips();
});
</script>

<script>
    // 更新数字并添加动画效果
    function updateNumberWithAnimation(elementId, newValue) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const oldValue = element.textContent;
        if (oldValue !== String(newValue) && oldValue !== '加载中...') {
            // 添加数字滚动效果
            animateNumber(element, parseInt(oldValue) || 0, newValue, 800);

            // 添加卡片闪烁效果
            const card = element.closest('.stat-card, .spider-card');
            if (card) {
                card.style.boxShadow = '0 0 20px rgba(255, 215, 0, 0.6)';
                setTimeout(() => {
                    card.style.boxShadow = '';
                }, 1000);
            }
        } else if (oldValue === '加载中...') {
            element.textContent = newValue;
        }
    }

    // 数字滚动动画
    function animateNumber(element, start, end, duration) {
        const startTime = performance.now();
        const difference = end - start;

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.round(start + difference * easeOutQuart);

            element.textContent = current;
            element.classList.add('stats-number', 'updated');

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            } else {
                setTimeout(() => {
                    element.classList.remove('updated');
                }, 300);
            }
        }

        requestAnimationFrame(updateNumber);
    }

    // 更新今日访问统计和蜘蛛统计
    function updateTodayStats() {
        fetch('?mod=datastats&ajax=1')
            .then(response => response.json())
            .then(data => {
                // 更新今日访问统计
                if (data.today_stats) {
                    updateNumberWithAnimation('todayTotalVisits', data.today_stats.total_visits || 0);
                    updateNumberWithAnimation('todaySiteVisits', data.today_stats.total_sites || 0);
                    updateNumberWithAnimation('todayArticleVisits', data.today_stats.total_articles || 0);
                    updateNumberWithAnimation('todayOutlinks', data.today_stats.total_outlinks || 0);
                }

                // 更新蜘蛛统计
                if (data.spider_stats) {
                    updateNumberWithAnimation('spiderGoogle', data.spider_stats.google || 0);
                    updateNumberWithAnimation('spiderBaidu', data.spider_stats.baidu || 0);
                    updateNumberWithAnimation('spiderBing', data.spider_stats.bing || 0);
                    updateNumberWithAnimation('spiderSogou', data.spider_stats.sogou || 0);
                    updateNumberWithAnimation('spiderSo360', data.spider_stats.so360 || 0);
                    updateNumberWithAnimation('spiderBytedance', data.spider_stats.bytedance || 0);
                }
            })
            .catch(() => {
                console.log('今日统计服务不可用');
                // 设置默认值
                const todayElements = ['todayTotalVisits', 'todaySiteVisits', 'todayArticleVisits', 'todayOutlinks'];
                const spiderElements = ['spiderGoogle', 'spiderBaidu', 'spiderBing', 'spiderSogou', 'spiderSo360', 'spiderBytedance'];

                todayElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.textContent = '0';
                });

                spiderElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.textContent = '0';
                });
            });
    }

    // 添加卡片点击效果
    function addCardClickEffects() {
        document.querySelectorAll('.stat-card, .spider-card').forEach(card => {
            card.addEventListener('click', function() {
                // 点击波纹效果
                const ripple = document.createElement('div');
                ripple.style.cssText = `
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.6);
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    pointer-events: none;
                `;

                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = (rect.width / 2 - size / 2) + 'px';
                ripple.style.top = (rect.height / 2 - size / 2) + 'px';

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }

    // 添加波纹动画CSS
    const rippleStyle = document.createElement('style');
    rippleStyle.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(rippleStyle);

    // 页面加载时立即执行
    document.addEventListener('DOMContentLoaded', function() {
        updateTodayStats();
        addCardClickEffects();

        // 设置定时器 - 今日统计每5分钟更新
        setInterval(updateTodayStats, 300000);
    });
</script>

</body>
</html>